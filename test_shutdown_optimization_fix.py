#!/usr/bin/env python3
"""
Test script to verify the shutdown optimization fix.
This script tests that settings are saved properly during shutdown
without applying expensive theme operations.
"""

import sys
import os
import time
import logging
from unittest.mock import patch, MagicMock

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_shutdown_optimization():
    """Test that settings save is optimized during shutdown."""
    logger.info("Testing shutdown optimization...")
    
    try:
        # Mock PySide6 components to avoid GUI dependencies
        with patch('PySide6.QtWidgets.QApplication') as mock_app, \
             patch('PySide6.QtWidgets.QWidget') as mock_widget, \
             patch('PySide6.QtWidgets.QComboBox') as mock_combo, \
             patch('PySide6.QtWidgets.QSpinBox') as mock_spin, \
             patch('PySide6.QtWidgets.QCheckBox') as mock_check, \
             patch('PySide6.QtWidgets.QDoubleSpinBox') as mock_double_spin, \
             patch('PySide6.QtWidgets.QTextEdit') as mock_text, \
             patch('PySide6.QtWidgets.QLineEdit') as mock_line, \
             patch('PySide6.QtWidgets.QPushButton') as mock_button, \
             patch('PySide6.QtWidgets.QSlider') as mock_slider, \
             patch('PySide6.QtWidgets.QLabel') as mock_label, \
             patch('PySide6.QtWidgets.QMessageBox') as mock_msgbox:
            
            # Mock the settings manager
            with patch('src.gui.handlers.settings_handlers.settings_manager') as mock_settings_manager:
                mock_settings_manager.set_value = MagicMock()
                mock_settings_manager.backup_settings = MagicMock(return_value=(True, 'backup.json'))
                
                # Import the SettingsHandlers class
                from src.gui.handlers.settings_handlers import SettingsHandlers
                
                # Create a mock settings handler
                settings_handler = SettingsHandlers()
                
                # Mock all the UI elements
                settings_handler.theme_combo = MagicMock()
                settings_handler.theme_combo.currentText.return_value = "Dark Theme"
                settings_handler.color_scheme_combo = MagicMock()
                settings_handler.color_scheme_combo.currentText.return_value = "Dark"
                settings_handler.font_family_combo = MagicMock()
                settings_handler.font_family_combo.currentText.return_value = "Arial"
                settings_handler.font_size_combo = MagicMock()
                settings_handler.font_size_combo.currentText.return_value = "Normal"
                settings_handler.language_combo = MagicMock()
                settings_handler.language_combo.currentText.return_value = "English"
                
                # Mock segmentation settings
                settings_handler.default_seg_method = MagicMock()
                settings_handler.default_seg_method.currentText.return_value = "Watershed"
                settings_handler.default_epochs = MagicMock()
                settings_handler.default_epochs.value.return_value = 100
                settings_handler.default_min_labels = MagicMock()
                settings_handler.default_min_labels.value.return_value = 2
                settings_handler.default_max_labels = MagicMock()
                settings_handler.default_max_labels.value.return_value = 10
                settings_handler.default_color_palette = MagicMock()
                settings_handler.default_color_palette.currentText.return_value = "viridis"
                
                # Mock other required attributes
                for attr in ['default_intensity_features', 'default_edge_features', 'default_texture_features']:
                    setattr(settings_handler, attr, MagicMock())
                    getattr(settings_handler, attr).isChecked.return_value = True
                
                for attr in ['default_sigma_min', 'default_sigma_max', 'default_n_estimators', 
                           'default_max_depth', 'default_max_samples', 'default_brush_size',
                           'default_input_size', 'default_iou_threshold', 'default_conf_threshold',
                           'default_max_det', 'default_artifact_sensitivity', 'default_duplicate_sensitivity',
                           'default_points_per_side', 'default_pred_iou_thresh', 'default_stability_score_thresh',
                           'default_box_nms_thresh', 'default_min_mask_area', 'default_img_width',
                           'default_img_height', 'default_grid_opacity']:
                    setattr(settings_handler, attr, MagicMock())
                    getattr(settings_handler, attr).value.return_value = 1.0
                
                for attr in ['default_artifact_preset', 'default_gemini_model', 'export_format']:
                    setattr(settings_handler, attr, MagicMock())
                    getattr(settings_handler, attr).currentText.return_value = "default"
                
                settings_handler.prompt_templates_text = MagicMock()
                settings_handler.prompt_templates_text.toPlainText.return_value = "test prompt"
                settings_handler.export_dir = MagicMock()
                settings_handler.export_dir.text.return_value = "/tmp"
                settings_handler.auto_resize = MagicMock()
                settings_handler.auto_resize.isChecked.return_value = True
                settings_handler.default_grid_color_button = MagicMock()
                settings_handler.default_grid_color_button.styleSheet.return_value = "background-color: rgba(255, 255, 255, 180);"
                
                # Mock the update methods
                settings_handler.update_processing_page = MagicMock()
                settings_handler.update_trainable_segmentation_page = MagicMock()
                settings_handler.update_grain_analysis_page = MagicMock()
                settings_handler.update_mobilesam_settings = MagicMock()
                settings_handler.update_yolov8_settings = MagicMock()
                settings_handler.update_ai_assistant_settings = MagicMock()
                settings_handler.apply_theme = MagicMock()
                
                # Test 1: Normal save (not during shutdown)
                logger.info("Test 1: Normal save operation")
                start_time = time.time()
                result = settings_handler.save_settings()
                normal_time = time.time() - start_time
                
                assert result == True, "Normal save should succeed"
                assert settings_handler.apply_theme.called, "Theme should be applied during normal save"
                assert settings_handler.update_processing_page.called, "Page updates should occur during normal save"
                logger.info(f"Normal save completed in {normal_time:.3f} seconds")
                
                # Reset mocks
                settings_handler.apply_theme.reset_mock()
                settings_handler.update_processing_page.reset_mock()
                
                # Test 2: Save during shutdown (simulate closeEvent in call stack)
                logger.info("Test 2: Save during shutdown")
                
                def mock_closeEvent():
                    """Mock function to simulate closeEvent in call stack"""
                    start_time = time.time()
                    result = settings_handler.save_settings()
                    shutdown_time = time.time() - start_time
                    return result, shutdown_time
                
                # Patch the function name to simulate closeEvent
                with patch('inspect.stack') as mock_stack:
                    # Create mock frame with closeEvent function name
                    mock_frame = MagicMock()
                    mock_frame.function = 'closeEvent'
                    mock_stack.return_value = [mock_frame]
                    
                    result, shutdown_time = mock_closeEvent()
                    
                    assert result == True, "Shutdown save should succeed"
                    assert not settings_handler.apply_theme.called, "Theme should NOT be applied during shutdown"
                    assert not settings_handler.update_processing_page.called, "Page updates should NOT occur during shutdown"
                    logger.info(f"Shutdown save completed in {shutdown_time:.3f} seconds")
                    
                    # Verify optimization
                    if shutdown_time < normal_time:
                        logger.info(f"✓ Shutdown optimization successful: {normal_time:.3f}s → {shutdown_time:.3f}s ({((normal_time - shutdown_time) / normal_time * 100):.1f}% faster)")
                    else:
                        logger.warning(f"Shutdown time ({shutdown_time:.3f}s) not significantly faster than normal time ({normal_time:.3f}s)")
                
                # Test 3: Save during handle_app_shutdown
                logger.info("Test 3: Save during handle_app_shutdown")
                
                with patch('inspect.stack') as mock_stack:
                    # Create mock frame with handle_app_shutdown function name
                    mock_frame = MagicMock()
                    mock_frame.function = 'handle_app_shutdown'
                    mock_stack.return_value = [mock_frame]
                    
                    settings_handler.apply_theme.reset_mock()
                    settings_handler.update_processing_page.reset_mock()
                    
                    result = settings_handler.save_settings()
                    
                    assert result == True, "handle_app_shutdown save should succeed"
                    assert not settings_handler.apply_theme.called, "Theme should NOT be applied during handle_app_shutdown"
                    assert not settings_handler.update_processing_page.called, "Page updates should NOT occur during handle_app_shutdown"
                    logger.info("handle_app_shutdown save optimization verified")
                
                logger.info("✓ All shutdown optimization tests passed!")
                return True
                
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_apply_theme_optimization():
    """Test that apply_theme is optimized during shutdown."""
    logger.info("Testing apply_theme optimization...")
    
    try:
        # Mock PySide6 components
        with patch('PySide6.QtWidgets.QApplication') as mock_app, \
             patch('PySide6.QtWidgets.QWidget') as mock_widget:
            
            from src.gui.handlers.settings_handlers import SettingsHandlers
            
            settings_handler = SettingsHandlers()
            
            # Mock required UI elements
            settings_handler.theme_combo = MagicMock()
            settings_handler.theme_combo.currentText.return_value = "Dark Theme"
            settings_handler.color_scheme_combo = MagicMock()
            settings_handler.color_scheme_combo.currentText.return_value = "Dark"
            settings_handler.font_family_combo = MagicMock()
            settings_handler.font_family_combo.currentText.return_value = "Arial"
            settings_handler.font_size_combo = MagicMock()
            settings_handler.font_size_combo.currentText.return_value = "Normal"
            
            # Test 1: Normal apply_theme
            logger.info("Test 1: Normal apply_theme operation")
            with patch('src.gui.styles.theme_config.apply_theme') as mock_theme_apply:
                result = settings_handler.apply_theme(show_message=False)
                # Should return True but may fail due to missing UI elements, that's ok
                logger.info(f"Normal apply_theme result: {result}")
            
            # Test 2: apply_theme during shutdown
            logger.info("Test 2: apply_theme during shutdown")
            
            def mock_closeEvent_theme():
                """Mock function to simulate closeEvent in call stack"""
                return settings_handler.apply_theme(show_message=False)
            
            with patch('inspect.stack') as mock_stack:
                # Create mock frame with closeEvent function name
                mock_frame = MagicMock()
                mock_frame.function = 'closeEvent'
                mock_stack.return_value = [mock_frame]
                
                result = mock_closeEvent_theme()
                
                assert result == True, "apply_theme should return True immediately during shutdown"
                logger.info("✓ apply_theme shutdown optimization verified")
            
            logger.info("✓ All apply_theme optimization tests passed!")
            return True
            
    except Exception as e:
        logger.error(f"apply_theme test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("Starting shutdown optimization tests...")
    
    success = True
    
    # Test save_settings optimization
    if not test_shutdown_optimization():
        success = False
    
    # Test apply_theme optimization
    if not test_apply_theme_optimization():
        success = False
    
    if success:
        logger.info("🎉 All tests passed! Shutdown optimization is working correctly.")
        sys.exit(0)
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")
        sys.exit(1)