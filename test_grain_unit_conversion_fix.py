#!/usr/bin/env python3
"""
Test script to verify the grain analysis unit conversion fixes.

This test verifies:
1. Unit preference persistence: Input/output unit selections are properly saved and restored
2. Post-segmentation unit display: Results immediately reflect chosen output unit after segmentation

The fixes address:
- Issue 1: Unit combos not being properly saved/restored from project state
- Issue 2: Results initially displayed in micrometers regardless of selected output unit
"""

import os
import sys
import pandas as pd
from unittest.mock import Mock, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_unit_conversion_methods():
    """Test the unit conversion helper methods."""
    print("Testing unit conversion methods...")

    try:
        # Test the conversion logic directly without creating the full widget
        def get_unit_conversion_factor(from_unit, to_unit):
            """Get conversion factor from one unit to another."""
            # Define conversion factors to micrometers (base unit)
            to_micrometers = {
                'nanometer': 0.001,
                'micrometer': 1.0,
                'millimeter': 1000.0,
                'meter': 1000000.0
            }

            if from_unit not in to_micrometers or to_unit not in to_micrometers:
                return 1.0

            # Convert from source unit to micrometers, then to target unit
            factor = to_micrometers[from_unit] / to_micrometers[to_unit]
            return factor

        def get_unit_symbol(unit):
            """Get the symbol for a unit."""
            symbols = {
                'nanometer': 'nm',
                'micrometer': 'µm',
                'millimeter': 'mm',
                'meter': 'm'
            }
            return symbols.get(unit, unit)
        
        # Test conversion factors
        test_cases = [
            ('micrometer', 'micrometer', 1.0),
            ('micrometer', 'millimeter', 0.001),
            ('micrometer', 'meter', 0.000001),
            ('micrometer', 'nanometer', 1000.0),
            ('millimeter', 'micrometer', 1000.0),
            ('meter', 'micrometer', 1000000.0),
            ('nanometer', 'micrometer', 0.001),
        ]
        
        for from_unit, to_unit, expected in test_cases:
            factor = get_unit_conversion_factor(from_unit, to_unit)
            if abs(factor - expected) < 1e-10:
                print(f"✓ {from_unit} -> {to_unit}: {factor} (expected {expected})")
            else:
                print(f"✗ {from_unit} -> {to_unit}: {factor} (expected {expected})")
                return False

        # Test unit symbols
        symbol_cases = [
            ('micrometer', 'µm'),
            ('millimeter', 'mm'),
            ('meter', 'm'),
            ('nanometer', 'nm'),
        ]

        for unit, expected_symbol in symbol_cases:
            symbol = get_unit_symbol(unit)
            if symbol == expected_symbol:
                print(f"✓ {unit} symbol: {symbol}")
            else:
                print(f"✗ {unit} symbol: {symbol} (expected {expected_symbol})")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ FAILED: Error testing unit conversion methods: {e}")
        return False


def test_state_saving_unit_persistence():
    """Test that unit preferences are properly saved in state."""
    print("\nTesting unit preference persistence in state saving...")
    
    try:
        from gui.grain_analysis_widget import GrainAnalysisWidget
        from PySide6.QtWidgets import QApplication, QComboBox
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create widget and mock the necessary components
        widget = GrainAnalysisWidget()
        
        # Mock the combo boxes
        widget.input_unit_combo = QComboBox()
        widget.input_unit_combo.addItems(["micrometer", "millimeter", "meter", "nanometer"])
        widget.input_unit_combo.setCurrentText("millimeter")
        
        widget.output_unit_combo = QComboBox()
        widget.output_unit_combo.addItems(["micrometer", "millimeter", "meter", "nanometer"])
        widget.output_unit_combo.setCurrentText("meter")
        
        # Mock other required attributes
        widget.grain_image_file_path = "test_image.jpg"
        widget.state_modified = True
        widget.view = Mock()
        widget.view.scale_line = None
        widget.grain_current_scale_factor = 1.5
        widget.scale_unit_combo = Mock()
        widget.scale_unit_combo.currentText.return_value = "μm"
        widget.polygons_loaded = False
        widget.grain_annotations = None
        widget.grain_df = None
        widget.grain_df_micrometers = None
        
        # Mock the project and image ID lookup
        widget.project = Mock()
        widget._get_image_id_for_path = Mock(return_value="test_image_id")
        widget.project.save_grain_analysis_state = Mock()
        
        # Call save_grain_analysis_state
        result = widget.save_grain_analysis_state()
        
        # Verify the method was called and check the saved state
        if result and widget.project.save_grain_analysis_state.called:
            # Get the saved state from the mock call
            call_args = widget.project.save_grain_analysis_state.call_args
            image_id, state = call_args[0]
            
            # Check that unit preferences were saved
            if 'input_unit' in state and state['input_unit'] == 'millimeter':
                print("✓ Input unit preference saved correctly")
            else:
                print(f"✗ Input unit preference not saved correctly: {state.get('input_unit')}")
                return False
            
            if 'output_unit' in state and state['output_unit'] == 'meter':
                print("✓ Output unit preference saved correctly")
            else:
                print(f"✗ Output unit preference not saved correctly: {state.get('output_unit')}")
                return False
            
            return True
        else:
            print("✗ FAILED: save_grain_analysis_state did not complete successfully")
            return False
        
    except Exception as e:
        print(f"✗ FAILED: Error testing state saving: {e}")
        return False


def test_state_loading_unit_restoration():
    """Test that unit preferences are properly restored from state."""
    print("\nTesting unit preference restoration from state loading...")
    
    try:
        from gui.grain_analysis_widget import GrainAnalysisWidget
        from PySide6.QtWidgets import QApplication, QComboBox
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create widget and mock the necessary components
        widget = GrainAnalysisWidget()
        
        # Mock the combo boxes with default values
        widget.input_unit_combo = QComboBox()
        widget.input_unit_combo.addItems(["micrometer", "millimeter", "meter", "nanometer"])
        widget.input_unit_combo.setCurrentText("micrometer")  # Default
        
        widget.output_unit_combo = QComboBox()
        widget.output_unit_combo.addItems(["micrometer", "millimeter", "meter", "nanometer"])
        widget.output_unit_combo.setCurrentText("micrometer")  # Default
        
        # Mock the signal connections
        widget.on_input_unit_changed = Mock()
        widget.on_output_unit_changed = Mock()
        widget.input_unit_combo.currentTextChanged = Mock()
        widget.input_unit_combo.currentTextChanged.disconnect = Mock()
        widget.input_unit_combo.currentTextChanged.connect = Mock()
        widget.output_unit_combo.currentTextChanged = Mock()
        widget.output_unit_combo.currentTextChanged.disconnect = Mock()
        widget.output_unit_combo.currentTextChanged.connect = Mock()
        
        # Mock other required attributes
        widget.grain_image_file_path = "test_image.jpg"
        widget.project = Mock()
        widget._get_image_id_for_path = Mock(return_value="test_image_id")
        
        # Create a test state with different unit preferences
        test_state = {
            'input_unit': 'millimeter',
            'output_unit': 'meter',
            'annotations': None,
            'df': None
        }
        
        widget.project.load_grain_analysis_state = Mock(return_value=test_state)
        
        # Call load_grain_analysis_state_from_project
        result = widget.load_grain_analysis_state_from_project()
        
        # Verify the units were restored
        if result:
            # Check that the combo boxes were set to the correct values
            if widget.input_unit_combo.currentText() == 'millimeter':
                print("✓ Input unit preference restored correctly")
            else:
                print(f"✗ Input unit preference not restored: {widget.input_unit_combo.currentText()}")
                return False
            
            if widget.output_unit_combo.currentText() == 'meter':
                print("✓ Output unit preference restored correctly")
            else:
                print(f"✗ Output unit preference not restored: {widget.output_unit_combo.currentText()}")
                return False
            
            # Verify that signals were properly disconnected and reconnected
            if widget.input_unit_combo.currentTextChanged.disconnect.called:
                print("✓ Input unit signal properly disconnected during loading")
            else:
                print("✗ Input unit signal not disconnected during loading")
                return False
            
            if widget.output_unit_combo.currentTextChanged.disconnect.called:
                print("✓ Output unit signal properly disconnected during loading")
            else:
                print("✗ Output unit signal not disconnected during loading")
                return False
            
            return True
        else:
            print("✗ FAILED: load_grain_analysis_state_from_project did not complete successfully")
            return False
        
    except Exception as e:
        print(f"✗ FAILED: Error testing state loading: {e}")
        return False


def test_post_segmentation_unit_display():
    """Test that results immediately reflect chosen output unit after segmentation."""
    print("\nTesting post-segmentation unit display...")
    
    try:
        from gui.grain_analysis_widget import GrainAnalysisWidget
        from PySide6.QtWidgets import QApplication, QComboBox
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create widget and mock the necessary components
        widget = GrainAnalysisWidget()
        
        # Mock the combo boxes
        widget.output_unit_combo = QComboBox()
        widget.output_unit_combo.addItems(["micrometer", "millimeter", "meter", "nanometer"])
        widget.output_unit_combo.setCurrentText("millimeter")  # User selected millimeters
        
        # Mock other required attributes
        widget.grain_current_scale_factor = 1.0
        widget.results_widget = Mock()
        widget.results_widget.populate = Mock()
        widget.update_status = Mock()
        widget.polygons_loaded = False
        widget.display_image_on_scene = Mock()
        widget.auto_show_visualization_after_processing = Mock()
        widget.reload_results_button = Mock()
        widget.reload_results_button.setEnabled = Mock()
        widget.reload_results_button.setText = Mock()
        widget.update_action_states = Mock()
        widget.mark_state_as_modified = Mock()
        
        # Create test dataframe with results in micrometers (base unit)
        test_df = pd.DataFrame({
            'grain_id': [1, 2, 3],
            'area': [100.0, 200.0, 300.0],  # µm²
            'length': [10.0, 15.0, 20.0],   # µm
            'width': [8.0, 12.0, 16.0],     # µm
            'perimeter': [30.0, 45.0, 60.0] # µm
        })
        
        # Mock the refresh method to track if it's called
        original_refresh = widget.refresh_results_with_new_units
        widget.refresh_results_with_new_units = Mock()
        
        # Call on_processing_finished with test data
        widget.on_processing_finished(test_df, [], Mock())
        
        # Verify that refresh_results_with_new_units was called
        if widget.refresh_results_with_new_units.called:
            print("✓ refresh_results_with_new_units called after segmentation")
        else:
            print("✗ refresh_results_with_new_units not called after segmentation")
            return False
        
        # Verify that the dataframe was stored correctly
        if hasattr(widget, 'grain_df_micrometers') and widget.grain_df_micrometers is not None:
            print("✓ Original dataframe stored in micrometers for unit conversion")
        else:
            print("✗ Original dataframe not stored properly")
            return False
        
        # Test the actual unit conversion by calling the real refresh method
        widget.refresh_results_with_new_units = original_refresh
        widget.grain_df_micrometers = test_df.copy()
        widget.refresh_results_with_new_units()
        
        # Check if the dataframe was converted to millimeters
        if hasattr(widget, 'grain_df') and widget.grain_df is not None:
            # Length values should be converted from µm to mm (divided by 1000)
            expected_length = test_df['length'].iloc[0] / 1000  # 10.0 µm -> 0.01 mm
            actual_length = widget.grain_df['length'].iloc[0]
            
            if abs(actual_length - expected_length) < 1e-10:
                print(f"✓ Length values converted correctly: {actual_length} mm")
            else:
                print(f"✗ Length values not converted correctly: {actual_length} (expected {expected_length})")
                return False
            
            # Area values should be converted by factor squared
            expected_area = test_df['area'].iloc[0] / (1000 ** 2)  # 100.0 µm² -> 0.0001 mm²
            actual_area = widget.grain_df['area'].iloc[0]
            
            if abs(actual_area - expected_area) < 1e-10:
                print(f"✓ Area values converted correctly: {actual_area} mm²")
            else:
                print(f"✗ Area values not converted correctly: {actual_area} (expected {expected_area})")
                return False
        else:
            print("✗ Converted dataframe not available")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ FAILED: Error testing post-segmentation unit display: {e}")
        return False


def main():
    """Run all tests."""
    print("Testing Grain Analysis Unit Conversion Fixes")
    print("=" * 60)
    
    test1_passed = test_unit_conversion_methods()
    test2_passed = test_state_saving_unit_persistence()
    test3_passed = test_state_loading_unit_restoration()
    test4_passed = test_post_segmentation_unit_display()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed and test3_passed and test4_passed:
        print("✓ ALL TESTS PASSED - Unit conversion fixes should work correctly!")
        print("\nThe fixes include:")
        print("1. ✓ Unit preferences properly saved to and restored from project state")
        print("2. ✓ Signal disconnection during state loading prevents unwanted conversions")
        print("3. ✓ Post-segmentation results immediately reflect chosen output unit")
        print("4. ✓ Unit conversion methods work correctly for all supported units")
        print("\nBoth reported issues should now be resolved!")
        return 0
    else:
        print("✗ SOME TESTS FAILED - Check the implementation")
        return 1


if __name__ == "__main__":
    sys.exit(main())
