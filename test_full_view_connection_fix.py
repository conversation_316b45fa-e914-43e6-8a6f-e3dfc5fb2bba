#!/usr/bin/env python3
"""
Test script to verify the AI Assistant full view button connection fix.

This test verifies that:
1. The full view button is connected only once in app.py
2. The connection is properly delegated to the handlers
3. No duplicate connections exist
"""

import os
import sys

def test_app_connection():
    """Test that app.py has the correct full view button connection."""
    print("Testing app.py full view button connection...")
    
    app_file = os.path.join(os.path.dirname(__file__), 'src', 'gui', 'app.py')
    
    if not os.path.exists(app_file):
        print(f"✗ FAILED: App file not found: {app_file}")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for the correct connection
    if 'ai_assistant_full_view_button.clicked.connect(self.open_ai_assistant_full_view)' in content:
        print("✓ Full view button connection found in app.py")
    else:
        print("✗ FAILED: Full view button connection not found in app.py")
        return False
    
    # Check for the delegation method
    if 'def open_ai_assistant_full_view(self):' in content:
        print("✓ Delegation method found in app.py")
    else:
        print("✗ FAILED: Delegation method not found in app.py")
        return False
    
    # Check that it ensures handlers are initialized
    if 'self._ensure_ai_assistant_handlers_initialized()' in content:
        print("✓ Handler initialization check found")
    else:
        print("✗ FAILED: Handler initialization check not found")
        return False
    
    return True

def test_handlers_no_duplicate():
    """Test that handlers don't have duplicate connection."""
    print("\nTesting handlers for duplicate connection removal...")
    
    handlers_file = os.path.join(os.path.dirname(__file__), 'src', 'gui', 'handlers', 'ai_assistant_handlers.py')
    
    if not os.path.exists(handlers_file):
        print(f"✗ FAILED: Handlers file not found: {handlers_file}")
        return False
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check that the duplicate connection is removed
    if 'ai_assistant_full_view_button.clicked.connect(self.open_ai_assistant_full_view)' not in content:
        print("✓ Duplicate connection removed from handlers")
    else:
        print("✗ FAILED: Duplicate connection still exists in handlers")
        return False
    
    # Check that the method still exists
    if 'def open_ai_assistant_full_view(self):' in content:
        print("✓ Full view method still exists in handlers")
    else:
        print("✗ FAILED: Full view method not found in handlers")
        return False
    
    return True

def main():
    """Run all tests."""
    print("Testing AI Assistant Full View Button Connection Fix")
    print("=" * 55)
    
    tests_passed = 0
    total_tests = 2
    
    if test_app_connection():
        tests_passed += 1
    
    if test_handlers_no_duplicate():
        tests_passed += 1
    
    print("\n" + "=" * 55)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ ALL TESTS PASSED - Full view button fix is working correctly!")
        print("\nThe fix ensures:")
        print("- Single connection in app.py with proper delegation")
        print("- Handler initialization before calling the method")
        print("- No duplicate connections that caused the double dialog issue")
        return True
    else:
        print("✗ SOME TESTS FAILED - Fix needs attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)