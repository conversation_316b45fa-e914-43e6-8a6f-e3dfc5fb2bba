import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Try importing ultralytics
try:
    logger.info("Attempting to import ultralytics...")
    from ultralytics import YOLO
    logger.info("Successfully imported YOLO from ultralytics")
    logger.info(f"YOLO version: {YOLO.__version__ if hasattr(YOLO, '__version__') else 'Unknown'}")
    logger.info(f"ultralytics path: {sys.modules['ultralytics'].__file__}")
except Exception as e:
    logger.error(f"Failed to import ultralytics: {e}")
    logger.error(f"Python path: {sys.path}")

# Try importing from the application's segmentation module
try:
    logger.info("Attempting to import from src.grainsight_components.core.segmentation...")
    from src.grainsight_components.core.segmentation import ULTRALYTICS_AVAILABLE, YOLO
    logger.info(f"ULTRALYTICS_AVAILABLE from segmentation module: {ULTRALYTICS_AVAILABLE}")
    logger.info(f"YOLO from segmentation module: {YOLO}")
except Exception as e:
    logger.error(f"Failed to import from segmentation module: {e}")

# Print Python environment information
logger.info(f"Python version: {sys.version}")
logger.info(f"Python executable: {sys.executable}")