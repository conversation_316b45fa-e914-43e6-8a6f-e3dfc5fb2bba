#!/usr/bin/env python3
"""
Test script to verify shutdown optimization improvements.
This script simulates the worker thread cleanup process to measure timing.
"""

import time
import logging
from PySide6.QtCore import QThread, QObject, Signal
from PySide6.QtWidgets import QApplication
import sys

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockWorker(QThread):
    """Mock worker thread for testing."""
    
    def __init__(self, name, stop_delay=0.5):
        super().__init__()
        self.name = name
        self.stop_delay = stop_delay
        self._should_stop = False
    
    def run(self):
        """Simulate work."""
        logger.info(f"{self.name} started")
        while not self._should_stop:
            time.sleep(0.1)
        time.sleep(self.stop_delay)  # Simulate cleanup time
        logger.info(f"{self.name} finished")
    
    def stop(self):
        """Stop the worker."""
        self._should_stop = True
    
    def cancel(self):
        """Cancel the worker (alias for stop)."""
        self.stop()

def test_old_shutdown_method(workers):
    """Test the old sequential shutdown method with 3-second timeouts."""
    logger.info("Testing OLD shutdown method (sequential with 3s timeouts)...")
    start_time = time.time()
    
    for worker in workers:
        worker.start()
    
    time.sleep(1)  # Let workers run for a bit
    
    # Old method: sequential cleanup with long timeouts
    for worker in workers:
        if worker.isRunning():
            logger.info(f"Stopping {worker.name}...")
            if hasattr(worker, 'stop'):
                worker.stop()
            worker.quit()
            if not worker.wait(3000):  # 3 second timeout
                logger.warning(f"{worker.name} did not stop gracefully, terminating...")
                worker.terminate()
                worker.wait(1000)  # 1 second for termination
            logger.info(f"{worker.name} cleaned up")
    
    end_time = time.time()
    total_time = end_time - start_time
    logger.info(f"OLD method total time: {total_time:.2f} seconds")
    return total_time

def test_new_shutdown_method(workers):
    """Test the new optimized shutdown method with 1-second timeouts."""
    logger.info("Testing NEW shutdown method (parallel signals + 1s timeouts)...")
    start_time = time.time()
    
    for worker in workers:
        worker.start()
    
    time.sleep(1)  # Let workers run for a bit
    
    # New method: parallel signal sending + reduced timeouts
    workers_to_cleanup = [(worker.name, worker, 'stop') for worker in workers if worker.isRunning()]
    
    if not workers_to_cleanup:
        logger.info("No running workers to cleanup")
        return 0
    
    # Phase 1: Send stop signals to all workers
    logger.info(f"Sending stop signals to {len(workers_to_cleanup)} workers...")
    for name, worker, stop_method in workers_to_cleanup:
        logger.info(f"Stopping {name}...")
        if stop_method == 'stop' and hasattr(worker, 'stop'):
            worker.stop()
        worker.quit()
    
    # Phase 2: Wait for all workers with reduced timeout
    logger.info("Waiting for workers to stop gracefully...")
    remaining_workers = []
    for name, worker, _ in workers_to_cleanup:
        if worker.isRunning():
            if not worker.wait(1000):  # 1 second timeout (reduced from 3)
                logger.warning(f"{name} did not stop gracefully within 1 second")
                remaining_workers.append((name, worker))
            else:
                logger.info(f"{name} stopped gracefully")
    
    # Phase 3: Terminate remaining workers
    if remaining_workers:
        logger.info(f"Terminating {len(remaining_workers)} remaining workers...")
        for name, worker in remaining_workers:
            if worker.isRunning():
                logger.warning(f"Terminating {name}...")
                worker.terminate()
                worker.wait(500)  # 0.5 second (reduced from 1)
                logger.info(f"{name} terminated")
    
    end_time = time.time()
    total_time = end_time - start_time
    logger.info(f"NEW method total time: {total_time:.2f} seconds")
    return total_time

def main():
    """Main test function."""
    app = QApplication(sys.argv)
    
    # Test with multiple workers to simulate real application
    logger.info("Creating mock workers...")
    
    # Test old method
    old_workers = [
        MockWorker("AI Assistant Gemini", 0.3),
        MockWorker("Batch Processing", 0.4),
        MockWorker("Grain Processing Worker", 0.2),
        MockWorker("Grain Patch Processing", 0.3),
        MockWorker("Grain Processing Thread", 0.2)
    ]
    
    old_time = test_old_shutdown_method(old_workers)
    
    # Wait a bit between tests
    time.sleep(2)
    
    # Test new method
    new_workers = [
        MockWorker("AI Assistant Gemini", 0.3),
        MockWorker("Batch Processing", 0.4),
        MockWorker("Grain Processing Worker", 0.2),
        MockWorker("Grain Patch Processing", 0.3),
        MockWorker("Grain Processing Thread", 0.2)
    ]
    
    new_time = test_new_shutdown_method(new_workers)
    
    # Calculate improvement
    improvement = old_time - new_time
    improvement_percent = (improvement / old_time) * 100 if old_time > 0 else 0
    
    logger.info("\n" + "="*50)
    logger.info("SHUTDOWN OPTIMIZATION RESULTS:")
    logger.info(f"Old method time: {old_time:.2f} seconds")
    logger.info(f"New method time: {new_time:.2f} seconds")
    logger.info(f"Time saved: {improvement:.2f} seconds")
    logger.info(f"Improvement: {improvement_percent:.1f}%")
    logger.info("="*50)
    
    app.quit()

if __name__ == "__main__":
    main()