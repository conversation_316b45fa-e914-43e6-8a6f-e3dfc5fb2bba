#!/usr/bin/env python3
"""
Test script to verify the duplicate full view dialog fix.

This test verifies that:
1. Only one full view button connection exists in app.py
2. The duplicate connection in ai_assistant_page.py is commented out
3. No duplicate FullViewDialog instantiation occurs
"""

import os
import sys

def test_app_single_connection():
    """Test that app.py has only one full view button connection."""
    print("Testing app.py for single full view button connection...")
    
    app_file = os.path.join(os.path.dirname(__file__), 'src', 'gui', 'app.py')
    
    if not os.path.exists(app_file):
        print(f"✗ FAILED: App file not found: {app_file}")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for the correct connection
    connection_count = content.count('ai_assistant_full_view_button.clicked.connect(self.open_ai_assistant_full_view)')
    
    if connection_count == 1:
        print("✓ Single connection found in app.py")
        return True
    elif connection_count == 0:
        print("✗ FAILED: No connection found in app.py")
        return False
    else:
        print(f"✗ FAILED: Multiple connections found in app.py ({connection_count})")
        return False

def test_ai_assistant_page_no_connection():
    """Test that ai_assistant_page.py has no active full view connection."""
    print("\nTesting ai_assistant_page.py for commented out connection...")
    
    page_file = os.path.join(os.path.dirname(__file__), 'src', 'ai_assistant_components', 'src', 'ui', 'pages', 'ai_assistant_page.py')
    
    if not os.path.exists(page_file):
        print(f"✗ FAILED: AI Assistant page file not found: {page_file}")
        return False
    
    with open(page_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check that the connection is commented out
    active_connection = 'self.full_view_button.clicked.connect(self._open_full_view)'
    commented_connection = '# self.full_view_button.clicked.connect(self._open_full_view)'
    
    if commented_connection in content and active_connection not in content:
        print("✓ Connection properly commented out in ai_assistant_page.py")
        return True
    elif active_connection in content:
        print("✗ FAILED: Active connection still exists in ai_assistant_page.py")
        return False
    else:
        print("✗ FAILED: Connection not found (commented or active) in ai_assistant_page.py")
        return False

def test_handlers_connection_exists():
    """Test that handlers still have the connection method."""
    print("\nTesting handlers for open_ai_assistant_full_view method...")
    
    handlers_file = os.path.join(os.path.dirname(__file__), 'src', 'gui', 'handlers', 'ai_assistant_handlers.py')
    
    if not os.path.exists(handlers_file):
        print(f"✗ FAILED: Handlers file not found: {handlers_file}")
        return False
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check that the method exists
    if 'def open_ai_assistant_full_view(self):' in content:
        print("✓ open_ai_assistant_full_view method exists in handlers")
        return True
    else:
        print("✗ FAILED: open_ai_assistant_full_view method not found in handlers")
        return False

def main():
    """Run all tests."""
    print("Testing Duplicate Full View Dialog Fix")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 3
    
    if test_app_single_connection():
        tests_passed += 1
    
    if test_ai_assistant_page_no_connection():
        tests_passed += 1
    
    if test_handlers_connection_exists():
        tests_passed += 1
    
    print("\n" + "=" * 40)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ ALL TESTS PASSED - Duplicate full view dialog fix is working correctly!")
        print("\nThe fix ensures:")
        print("- Only one connection exists in app.py")
        print("- Duplicate connection in ai_assistant_page.py is commented out")
        print("- Handler method is still available for delegation")
        print("- No duplicate dialogs should appear when clicking the full view button")
        return True
    else:
        print("✗ SOME TESTS FAILED - Please check the implementation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)