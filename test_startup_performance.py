
#!/usr/bin/env python3
"""
Startup Performance Test Script

This script measures the startup time of VisionLab Ai application.
"""

import time
import subprocess
import sys
import os

def test_startup_time(iterations=3):
    """Test startup time multiple times and return average."""
    times = []
    
    for i in range(iterations):
        print(f"Testing startup time - iteration {i+1}/{iterations}")
        
        start_time = time.time()
        
        # Run the application and measure time until window is shown
        try:
            # Activate virtual environment and run main.py
            if os.name == 'nt':  # Windows
                cmd = ['venv\Scripts\activate.bat', '&&', 'python', 'main.py', '--test-startup']
                process = subprocess.Popen(' '.join(cmd), shell=True)
            else:  # Unix-like
                cmd = ['source', 'venv/bin/activate', '&&', 'python', 'main.py', '--test-startup']
                process = subprocess.Popen(' '.join(cmd), shell=True)
            
            # Wait for process to start and then terminate
            time.sleep(5)  # Allow app to fully load
            process.terminate()
            process.wait()
            
            end_time = time.time()
            startup_time = end_time - start_time
            times.append(startup_time)
            
            print(f"Startup time: {startup_time:.2f} seconds")
            
        except Exception as e:
            print(f"Error during test: {e}")
            continue
    
    if times:
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"
Results after {len(times)} tests:")
        print(f"Average startup time: {avg_time:.2f} seconds")
        print(f"Minimum startup time: {min_time:.2f} seconds")
        print(f"Maximum startup time: {max_time:.2f} seconds")
        
        return avg_time
    else:
        print("No successful tests completed")
        return None

if __name__ == "__main__":
    print("VisionLab Ai Startup Performance Test")
    print("=====================================")
    
    avg_time = test_startup_time()
    
    if avg_time:
        if avg_time < 5:
            print("
[EXCELLENT] Excellent startup performance!")
        elif avg_time < 10:
            print("
[GOOD] Good startup performance!")
        elif avg_time < 15:
            print("
[WARNING] Acceptable startup performance")
        else:
            print("
[ERROR] Slow startup performance - needs optimization")
